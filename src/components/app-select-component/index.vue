<template>
  <el-select
    v-model="app"
    placeholder="请选择当前应用"
    filterable
    v-bind="$attrs"
    v-on="$listeners"
    @change="change"
    :style="{ width: comWidth }"
  >
    <el-option
      v-for="item in appList"
      :key="item.id"
      :label="item.name"
      :value="item[selectKey]"
    >
      <span style="float: left">{{ item.name }}</span>
      <span
        style="float: right; color: #8492a6; font-size: 13px; margin-left: 5px"
      >
        {{ item.code }}
        <template v-if="appClassification.get(item.genre)">
          /
          <i style="font-size: 12px">{{ appClassification.get(item.genre) }}</i>
        </template>
      </span>
    </el-option>
  </el-select>
</template>

<script>
import { appClassification } from '@/map/common'

export default {
  name: 'app-select-component',
  props: {
    // 使用哪个字段作为v-model的值
    selectKey: {
      type: String,
      default: 'code',
    },
    value: {
      type: [Number, String, Array],
    },
    isShowAll: {
      type: Boolean,
      default: true,
    },
    width: {
      type: [Number, String],
    },
    appFilter: {
      type: Function,
    },
  },
  data() {
    return {
      appClassification,
    }
  },
  computed: {
    app: {
      get() {
        return this.value
      },
      set(app) {
        this.$emit('input', app)
      },
    },
    appList() {
      // 这两个账号不能显示全部
      const userNameList = this.$store.state.global.outUserNameList
      const userName = this.$store.state.user.name
      let showAll = this.isShowAll

      if (userNameList.includes(userName)) {
        showAll = false
      }

      const appList = this.$store.state.global.appList.filter(it => {
        if (this.appFilter) {
          if (it.code === 0) return true
          return this.appFilter(it)
        }
        return true
      })
      // return appList.filter(it => (showAll ? true : it.code !== 0))
      return appList.filter(it => showAll || it.code !== 0)
    },
    comWidth() {
      return typeof this.width === 'number' ? this.width + 'px' : this.width
    },
  },
  created() {
    this.emitChangeApp(this.value)
  },
  methods: {
    change(app) {
      this.emitChangeApp(app)
      // 只有用户选择的时候才调用
      this.$emit('user-change', this.getCurrentApp(app))
    },
    emitChangeApp(app) {
      // 创建的时候就会调用一次
      this.$emit('change-app', this.getCurrentApp(app))
    },
    getCurrentApp(selectKey) {
      return this.appList.find(it => it[this.selectKey] === selectKey)
    },
    getAppList() {
      return this.appList
    },
  },
}
</script>

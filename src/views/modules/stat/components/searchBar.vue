<template>
  <div>
    <el-form :inline="true" :model="formInline">
      <el-form-item label="账户ID">
        <el-select
          v-model="formInline.accountIds"
          multiple
          filterable
          placeholder="请选择"
          collapse-tags
        >
          <el-option
            v-for="item in options"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="应用" prop="appId">
        <app-select-component v-model="formInline.appId" :app-filter="appFilter" />
      </el-form-item>
      <el-form-item label="投放媒体">
        <el-select
          v-model="formInline.unionType"
          placeholder="请选择"
          collapse-tags
        >
          <el-option
            v-for="item in unionTypeList"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="投放渠道">
        <el-select
          v-model="formInline.channel"
          placeholder="请选择"
          collapse-tags
        >
          <el-option
            v-for="item in channelList"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="代理商">
        <el-select
          v-model="formInline.agent"
          placeholder="请选择"
          collapse-tags
        >
          <el-option
            v-for="item in agentList"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否聚合">
        <el-select
          v-model="formInline.isAgg"
          placeholder="请选择"
          collapse-tags
        >
          <el-option
            label="是"
            :value="1"
          ></el-option>
          <el-option
            label="否"
            :value="0"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="formInline.date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">查询</el-button>
        <el-button @click="onReseat">重置</el-button>
        <slot></slot>
        <slot name="otherOption"></slot>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { agentList } from '@/map/agent'

export default {
  name: 'SearchBar',
  props: {
    options: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      formInline: {
        accountIds: [],
        appId: '',
        date: [],
        unionType: '',
        channel: '',
        agent: '',
        isAgg: 0,
      },
      channelList: [
        '快手',
        '头条',
        '广点通',
        '百度',
        'oppo信息流',
        'vivo信息流',
        'honor信息流',
        '小米信息流',
        '华为信息流',
        'uc',
      ],
      brandList: ['荣耀', '华为', '小米', 'oppo', 'vivo'],
      agentList: agentList,
      unionTypeList: [
        { label: 'oppo联盟', value: 1 },
        { label: 'oppo站内', value: 2 },
        { label: 'vivo联盟', value: 3 },
        { label: 'vivo站内', value: 4 },
        { label: '快手站内', value: 5 },
        { label: '快手联盟', value: 6 },
        { label: '穿山甲', value: 7 },
        { label: '抖音', value: 8 },
        { label: '优量汇', value: 9 },
        { label: 'uc浏览器', value: 10 },
        { label: '荣耀站内', value: 11 },
        { label: '荣耀联盟', value: 12 },
        { label: '华为站内', value: 13 },
        { label: '华为联盟', value: 14 },
        { label: '小米站内', value: 15 },
        { label: '小米联盟', value: 16 },
        { label: '百度站内', value: 17 },
        { label: '百度联盟', value: 18 },
      ],
    }
  },
  methods: {
    appFilter(app) {
      console.log('app filter')
      return app.code > 10226
    },
    onSubmit() {
      this.$emit('searchData', this.formInline)
    },
    onReseat() {
      this.formInline = {
        accountIds: [],
        appId: '',
        date: [],
        unionType: '',
        channel: '',
        agent: '',
        isAgg: 0,
      }
      this.$emit('searchData', this.formInline)
    },
  },
}
</script>
<style lang=""></style>

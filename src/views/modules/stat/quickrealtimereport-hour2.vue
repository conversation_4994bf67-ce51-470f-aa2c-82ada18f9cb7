<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="80%"
  >
    <div>
      <el-table
        ref="myTable"
        class="adapter-height"
        :data="dataList"
        border
        v-loading="dataListLoading"
        :header-cell-style="{ background: '#f8f9fa' }"
        style="width: 100%"
      >
        <el-table-column
          v-for="data in dataForm"
          :key="data.prop"
          :prop="data.prop"
          header-align="center"
          align="center"
          :label="data.label"
          min-width="90"
          :fixed="data.fixed"
        >
          <template slot-scope="scope">
            <div :style="data.style && data.style(scope.row[data.prop])">
              {{ data.formatter ? data.formatter(scope.row[data.prop]) : scope.row[data.prop] }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="100"
          label="操作"
          v-if="false"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="addCallback(scope.row)"
            >
              回传
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="editOCPC(scope.row)"
            >
              出价
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </el-dialog>
</template>

<script>
import { mixinElTableAdapterHeight } from '@/mixins'
import Decimal from 'decimal.js'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      visible: false,
      title: '',
      dataList: [],
      dataListLoading: false,
      dataForm: [
        { prop: 'ftime', label: '日期', fixed: 'left', formatter: this.toHour },
        { prop: 'cost', label: '消耗' },
        { prop: 'costExposureNum', label: '曝光' },
        { prop: 'adClickNum', label: '点击' },
        { prop: 'costEcpm', label: 'ECPM' },
        { prop: 'cpc', label: 'CPC' },
        { prop: 'ctr', label: 'CTR', formatter: this.toPercentage },
        { prop: 'formCnt', label: '表单提交量' },
        { prop: 'formPrice', label: 'CPA' },
        { prop: 'conversionRate', label: 'CVR', formatter: this.toPercentage2 },
        { prop: 'income', label: '预估收益' },
        { prop: 'xincome', label: '开屏收入' },
        { prop: 'ecpm', label: 'ECPM' },
        { prop: 'zong', label: '总收入' },
        { prop: 'roi', label: 'ROI', fixed: 'right', style: this.roiStyle },
      ],
    }
  },
  methods: {
    init(row) {
      this.visible = true
      this.title = row.accountId
      this.$nextTick(() => {
        this.getDataList(row.accountId, row.dt)
      })
    },
    getDataList(id, dt) {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/quickrealtimehourreport/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 1000,
          accountId: id,
          dt: dt
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
            .map(it => {
              if (it.adClickNum > 0) {
                it.formCnt = (it.adClickNum * it.conversionRate).toFixed(0)
                it.formPrice = (it.cost / (it.adClickNum * it.conversionRate)).toFixed(2)
              }
              it.zong = this.addEarnings(it.income, it.xincome)
              return it
            })
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    addEarnings(a, b) {
      if (!a && !b) {
        return 0
      }
      if (!b) {
        return a.toFixed(2)
      }
      return new Decimal(a).plus(b).toNumber().toFixed(2)
    },
    toPercentage(value) {
      return `${Math.round(value * 100)}%`
    },

    toHour(value) {
      //5 to 05:00
      return `${value.toString().padStart(2, '0')}:00`
    },

    toPercentage2(value) {
      return `${(value * 100).toFixed(2)}%`
    },
    roiStyle(value) {
      return value >= 1 ? 'color: #f56c6c' : 'color: #67c23a'
    }
  },
}
</script>

<style scoped>
</style>